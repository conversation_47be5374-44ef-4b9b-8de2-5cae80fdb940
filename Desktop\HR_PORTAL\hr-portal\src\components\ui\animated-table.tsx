import React, { ReactNode } from "react";
import { motion, AnimatePresence } from "framer-motion";

interface AnimatedTableProps {
  /**
   * Table headers as ReactNode
   */
  headers: ReactNode;
  
  /**
   * Whether the table is in loading state
   */
  isLoading?: boolean;
  
  /**
   * Loading component to show when isLoading is true
   */
  loadingComponent?: ReactNode;
  
  /**
   * Children to render as table rows
   */
  children: ReactNode;
  
  /**
   * Additional CSS classes for the table
   */
  className?: string;
}

/**
 * AnimatedTable component that adds Framer Motion animations to tables
 */
export function AnimatedTable({
  headers,
  isLoading = false,
  loadingComponent,
  children,
  className = "",
}: AnimatedTableProps) {
  return (
    <div className={`overflow-hidden rounded-md border ${className}`}>
      <div className="overflow-x-auto">
        <motion.table
          className="w-full divide-y divide-gray-200"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <motion.thead
            className="bg-gray-50"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            {headers}
          </motion.thead>
          <tbody className="divide-y divide-gray-200 bg-white">
            <AnimatePresence mode="wait">
              {isLoading ? (
                <tr>
                  <td colSpan={100} className="p-4 text-center">
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      {loadingComponent || (
                        <div className="py-8 text-center text-gray-500">
                          <svg
                            className="mx-auto h-12 w-12 animate-spin text-gray-400"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            ></circle>
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                          </svg>
                          <p className="mt-2 text-sm font-medium">Loading data...</p>
                        </div>
                      )}
                    </motion.div>
                  </td>
                </tr>
              ) : (
                <AnimatePresence>{children}</AnimatePresence>
              )}
            </AnimatePresence>
          </tbody>
        </motion.table>
      </div>
    </div>
  );
}

/**
 * AnimatedTableRow component that adds Framer Motion animations to table rows
 */
export function AnimatedTableRow({
  children,
  index = 0,
  className = "",
  ...props
}: {
  children: ReactNode;
  index?: number;
  className?: string;
  [key: string]: any;
}) {
  return (
    <motion.tr
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      transition={{
        duration: 0.3,
        delay: index * 0.05, // Staggered animation
        type: "spring",
        stiffness: 100,
        damping: 15,
      }}
      whileHover={{
        backgroundColor: "#f9fafb",
        transition: { duration: 0.1 },
      }}
      className={className}
      {...props}
    >
      {children}
    </motion.tr>
  );
}

/**
 * AnimatedTableCell component that adds Framer Motion animations to table cells
 */
export function AnimatedTableCell({
  children,
  className = "",
  ...props
}: {
  children: ReactNode;
  className?: string;
  [key: string]: any;
}) {
  return (
    <motion.td
      className={`px-4 py-2 text-sm ${className}`}
      whileHover={{ scale: 1.01 }}
      transition={{ duration: 0.1 }}
      {...props}
    >
      {children}
    </motion.td>
  );
}

/**
 * AnimatedTableHeader component that adds Framer Motion animations to table headers
 */
export function AnimatedTableHeader({
  children,
  onClick,
  className = "",
  ...props
}: {
  children: ReactNode;
  onClick?: () => void;
  className?: string;
  [key: string]: any;
}) {
  return (
    <motion.th
      className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 ${
        onClick ? "cursor-pointer hover:bg-gray-100" : ""
      } ${className}`}
      whileHover={onClick ? { backgroundColor: "#f3f4f6" } : {}}
      whileTap={onClick ? { scale: 0.98 } : {}}
      transition={{ duration: 0.2 }}
      onClick={onClick}
      {...props}
    >
      {children}
    </motion.th>
  );
}

/**
 * AnimatedSortIcon component for table column sorting
 */
export function AnimatedSortIcon({
  direction,
  active = false,
}: {
  direction: "asc" | "desc" | null;
  active?: boolean;
}) {
  // Default unsorted icon
  if (!active || !direction) {
    return (
      <motion.svg
        className="h-4 w-4 text-gray-400"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        whileHover={{ scale: 1.2, color: "#3b82f6" }}
        transition={{ duration: 0.2 }}
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"
        />
      </motion.svg>
    );
  }

  // Ascending sort icon
  if (direction === "asc") {
    return (
      <motion.svg
        className="h-4 w-4 text-blue-500"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        initial={{ scale: 1 }}
        animate={{
          scale: [1, 1.3, 1],
          rotate: [0, 0, 0],
        }}
        transition={{
          duration: 0.4,
          times: [0, 0.5, 1],
        }}
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M5 15l7-7 7 7"
        />
      </motion.svg>
    );
  }

  // Descending sort icon
  return (
    <motion.svg
      className="h-4 w-4 text-blue-500"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
      initial={{ scale: 1 }}
      animate={{
        scale: [1, 1.3, 1],
        rotate: [0, 0, 0],
      }}
      transition={{
        duration: 0.4,
        times: [0, 0.5, 1],
      }}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M19 9l-7 7-7-7"
      />
    </motion.svg>
  );
}

/**
 * AnimatedPagination component for table pagination
 */
export function AnimatedPagination({
  currentPage,
  totalPages,
  onPageChange,
  className = "",
}: {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  className?: string;
}) {
  return (
    <div className={`flex items-center justify-center space-x-1 py-4 ${className}`}>
      <motion.button
        onClick={() => onPageChange(Math.max(1, currentPage - 1))}
        disabled={currentPage === 1}
        className="inline-flex h-8 w-8 items-center justify-center rounded-md border border-gray-300 bg-white text-sm text-gray-500 disabled:opacity-50"
        whileHover={{ backgroundColor: "#f3f4f6" }}
        whileTap={{ scale: 0.95 }}
      >
        <span className="sr-only">Previous</span>
        <motion.svg
          className="h-5 w-5"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          fill="currentColor"
          whileHover={{ x: -2 }}
          transition={{ duration: 0.2 }}
        >
          <path
            fillRule="evenodd"
            d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
            clipRule="evenodd"
          />
        </motion.svg>
      </motion.button>

      {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
        <motion.button
          key={page}
          onClick={() => onPageChange(page)}
          className={`inline-flex h-8 w-8 items-center justify-center rounded-md border text-sm font-medium ${
            currentPage === page
              ? "border-[var(--primary)] bg-[var(--secondary)] text-[var(--primary)]"
              : "border-gray-300 bg-white text-gray-500 hover:bg-gray-50"
          }`}
          whileHover={{
            backgroundColor: currentPage === page ? "var(--secondary-hover)" : "#f3f4f6",
            y: -1,
          }}
          whileTap={{ scale: 0.95 }}
          animate={currentPage === page ? { scale: [1, 1.05, 1] } : { scale: 1 }}
          transition={{ duration: 0.2 }}
        >
          {page}
        </motion.button>
      ))}

      <motion.button
        onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
        disabled={currentPage === totalPages}
        className="inline-flex h-8 w-8 items-center justify-center rounded-md border border-gray-300 bg-white text-sm text-gray-500 disabled:opacity-50"
        whileHover={{ backgroundColor: "#f3f4f6" }}
        whileTap={{ scale: 0.95 }}
      >
        <span className="sr-only">Next</span>
        <motion.svg
          className="h-5 w-5"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          fill="currentColor"
          whileHover={{ x: 2 }}
          transition={{ duration: 0.2 }}
        >
          <path
            fillRule="evenodd"
            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
            clipRule="evenodd"
          />
        </motion.svg>
      </motion.button>
    </div>
  );
}

/**
 * useTableAnimation hook for table animations
 */
export function useTableAnimation() {
  const [isLoading, setIsLoading] = React.useState(false);
  
  // Function to animate table loading state
  const animateLoading = async (loadingFn: () => Promise<any>) => {
    setIsLoading(true);
    try {
      await loadingFn();
    } finally {
      setIsLoading(false);
    }
  };
  
  return {
    isLoading,
    setIsLoading,
    animateLoading,
  };
}
