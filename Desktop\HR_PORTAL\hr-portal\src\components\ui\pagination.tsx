import { ChevronLeft, ChevronRight } from "lucide-react";

interface PaginationProps {
  /**
   * Current page number
   */
  currentPage: number;

  /**
   * Total number of pages
   */
  totalPages: number;

  /**
   * Function to handle page change
   */
  onPageChange: (page: number) => void;

  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * A simple pagination component without animations
 */
export function Pagination({
  currentPage,
  totalPages,
  onPageChange,
  className = "",
}: PaginationProps) {
  return (
    <div className={`flex items-center gap-1 ${className}`}>
      {/* Previous page button */}
      <button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className={`p-1.5 rounded-md ${
          currentPage === 1
            ? "text-gray-400 cursor-not-allowed"
            : "text-gray-700 hover:bg-gray-100"
        }`}
      >
        <ChevronLeft className="h-4 w-4" />
      </button>

      {/* Page numbers */}
      {Array.from({ length: totalPages }).map((_, index) => {
        const pageNumber = index + 1;

        // Always show first page, last page, current page, and pages around current
        if (
          pageNumber === 1 ||
          pageNumber === totalPages ||
          (pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1)
        ) {
          return (
            <button
              key={pageNumber}
              onClick={() => onPageChange(pageNumber)}
              className={`px-2.5 py-1 rounded-md text-xs ${
                currentPage === pageNumber
                  ? "bg-[var(--primary)] text-[var(--primary-foreground)] font-medium"
                  : "text-gray-700 hover:bg-gray-100"
              }`}
            >
              {pageNumber}
            </button>
          );
        }

        // Show ellipsis for breaks in sequence
        if (
          (pageNumber === 2 && currentPage > 3) ||
          (pageNumber === totalPages - 1 && currentPage < totalPages - 2)
        ) {
          return (
            <span key={pageNumber} className="px-1 text-gray-500">
              ...
            </span>
          );
        }

        // Hide other pages
        return null;
      })}

      {/* Next page button */}
      <button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className={`p-1.5 rounded-md ${
          currentPage === totalPages
            ? "text-gray-400 cursor-not-allowed"
            : "text-gray-700 hover:bg-gray-100"
        }`}
      >
        <ChevronRight className="h-4 w-4" />
      </button>
    </div>
  );
}
